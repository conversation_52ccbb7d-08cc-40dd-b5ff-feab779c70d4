# 重构后的上传组件使用指南

## 概述

我们已经成功重构了文件上传组件，使其与任务中心模块集成。现在有三个主要的上传组件：

1. **FileUploader** - 重构后的文件上传组件
2. **FolderUploader** - 重构后的文件夹上传组件  
3. **UnifiedUploader** - 新的统一上传组件（推荐使用）

## 主要改进

### 🔄 任务中心集成
- 所有上传操作都通过任务中心进行管理
- 支持暂停、恢复、取消等操作
- 统一的进度监听和状态管理
- 断点续传支持

### 🎯 简化的API
- 移除了直接的OSS上传逻辑
- 使用 `useUploadTasks` hook 进行状态管理
- 自动处理用户认证和团队信息

### 📊 实时状态同步
- 组件状态与任务中心实时同步
- 支持多种状态：pending, uploading, success, error, paused
- 自动更新进度条和状态显示

## 使用示例

### 1. 基础文件上传

```tsx
import { FileUploader, UploadedFile } from '@/components/ui/file-uploader'

const MyComponent = () => {
  const [files, setFiles] = useState<UploadedFile[]>([])

  const handleUpload = (uploadedFiles: UploadedFile[]) => {
    console.log('上传完成:', uploadedFiles)
  }

  const handleError = (error: string) => {
    console.error('上传错误:', error)
  }

  return (
    <FileUploader
      folderUuid="your-folder-uuid"
      fileTypes={['video/*', 'image/*', 'audio/*']}
      maxFiles={5}
      maxSize={100 * 1024 * 1024} // 100MB
      multiple={true}
      onUpload={handleUpload}
      onError={handleError}
      value={files}
      onChange={setFiles}
      uploadModule="media"
    />
  )
}
```

### 2. 文件夹上传

```tsx
import { FolderUploader, FolderUploadedFile } from '@/components/ui/folder-uploader'
import { ResourceSource } from '@/types/resources'

const MyComponent = () => {
  const handleFolderUpload = (files: FolderUploadedFile[]) => {
    console.log('文件夹上传完成:', files)
  }

  return (
    <FolderUploader
      folderUuid="your-folder-uuid"
      resourceType={ResourceSource.MEDIA}
      fileTypes={['video/*', 'image/*', 'audio/*']}
      maxFiles={50}
      maxSize={100 * 1024 * 1024}
      onUpload={handleFolderUpload}
      onError={(error) => console.error(error)}
      buttonText="选择文件夹"
      showFileList={true}
    />
  )
}
```

### 3. 统一上传组件（推荐）

```tsx
import { UnifiedUploader, UnifiedUploadedFile } from '@/components/ui/unified-uploader'
import { ResourceSource } from '@/types/resources'

const MyComponent = () => {
  const [files, setFiles] = useState<UnifiedUploadedFile[]>([])

  return (
    <UnifiedUploader
      folderUuid="your-folder-uuid"
      fileTypes={['video/*', 'image/*', 'audio/*']}
      maxFiles={10}
      maxSize={100 * 1024 * 1024}
      multiple={true}
      supportFolder={true} // 启用文件夹上传
      onUpload={(files) => console.log('上传完成:', files)}
      onError={(error) => console.error('错误:', error)}
      value={files}
      onChange={setFiles}
      uploadModule="media"
      resourceType={ResourceSource.MEDIA}
    />
  )
}
```

### 4. 自定义渲染

```tsx
import { FileUploader, FileUploaderRenderProps } from '@/components/ui/file-uploader'

const CustomUploader = () => {
  const renderCustom = (props: FileUploaderRenderProps) => {
    const { isDragActive, getRootProps, getInputProps, uploadedFiles, removeFile } = props
    
    return (
      <div>
        <div {...getRootProps()} className="custom-dropzone">
          <input {...getInputProps()} />
          <p>{isDragActive ? '释放文件' : '点击或拖拽上传'}</p>
        </div>
        
        {uploadedFiles.map(file => (
          <div key={file.id} className="custom-file-item">
            <span>{file.file.name}</span>
            <span>{file.status}</span>
            <button onClick={() => removeFile(file.id)}>删除</button>
          </div>
        ))}
      </div>
    )
  }

  return (
    <FileUploader
      folderUuid="your-folder-uuid"
      renderCustomComponent={renderCustom}
      // ... 其他属性
    />
  )
}
```

## 属性说明

### 通用属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `folderUuid` | string | - | 目标文件夹UUID（必需） |
| `fileTypes` | string[] | `['video/*', 'image/*', 'audio/mpeg']` | 支持的文件类型 |
| `maxFiles` | number | 5 | 最大文件数量 |
| `maxSize` | number | 100MB | 单个文件最大大小 |
| `multiple` | boolean | true | 是否支持多文件选择 |
| `onUpload` | function | - | 上传完成回调 |
| `onError` | function | - | 错误回调 |
| `disabled` | boolean | false | 是否禁用 |
| `uploadModule` | string | 'media' | 上传模块类型 |

### UnifiedUploader 特有属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `supportFolder` | boolean | false | 是否支持文件夹上传 |
| `resourceType` | ResourceSource | MEDIA | 资源类型（用于文件夹上传） |

## 状态管理

### 文件状态

- `pending` - 等待上传
- `uploading` - 上传中
- `success` - 上传成功
- `error` - 上传失败
- `paused` - 已暂停

### 任务中心集成

组件会自动：
1. 创建上传任务
2. 监听任务状态变化
3. 同步进度和状态
4. 处理错误和重试

## 迁移指南

### 从旧版本迁移

1. **移除直接的OSS配置** - 不再需要手动配置OSS参数
2. **更新状态处理** - 新增了 `pending` 和 `paused` 状态
3. **使用任务中心** - 可以在任务中心查看和管理所有上传任务
4. **简化错误处理** - 错误信息通过任务中心统一管理

### 推荐做法

1. **优先使用 UnifiedUploader** - 功能最完整，API最简洁
2. **配合任务中心使用** - 可以实现更好的用户体验
3. **合理设置文件限制** - 根据实际需求设置 `maxFiles` 和 `maxSize`
4. **处理错误回调** - 提供友好的错误提示

## 注意事项

1. **用户认证** - 确保用户已登录，组件会自动获取用户信息
2. **文件夹权限** - 确保用户对目标文件夹有写入权限
3. **网络状态** - 组件支持断点续传，网络中断后可以恢复上传
4. **内存使用** - 大文件上传时注意内存使用情况

## 测试

可以使用 `uploader-test.tsx` 组件进行功能测试：

```tsx
import UploaderTest from '@/components/ui/uploader-test'

// 在你的路由或页面中使用
<UploaderTest />
```

这将显示所有三个组件的测试界面，方便验证功能是否正常。
