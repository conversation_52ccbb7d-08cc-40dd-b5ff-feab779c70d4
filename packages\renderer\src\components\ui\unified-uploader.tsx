import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { AlertCircle, CheckCircle, FileVideo, Image, Loader2, Upload, X, FolderOpen, Folder } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { Button } from './button'
import { useUploadTasks } from '@/hooks/useUploadTasks'
import { UploadTask } from '@app/shared/types/database.types'
import { TokenManager, TeamManager } from '@/libs/storage'
import { ResourceModule } from '@/libs/request/api/resource'
import { ResourceSource } from '@/types/resources'

declare module 'react' {
  interface InputHTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
    webkitdirectory?: boolean
    directory?: boolean
  }
}

export interface UnifiedUploadedFile {
  id: string
  file: File
  preview?: string
  relativePath?: string // 用于文件夹上传
  taskId?: number // 关联的任务ID
  status: 'pending' | 'uploading' | 'success' | 'error' | 'paused'
  error?: string
  url?: string
  fileName?: string
  progress?: number // 上传进度 (0-1)
  objectId?: string
  fileMd5?: string
  folderUuid: string
}

export interface UnifiedUploaderProps {
  folderUuid: string
  fileTypes?: string[]
  maxFiles?: number
  maxSize?: number
  multiple?: boolean
  supportFolder?: boolean // 是否支持文件夹上传
  onUpload?: (files: UnifiedUploadedFile[]) => void
  onError?: (error: string) => void
  className?: string
  disabled?: boolean
  value?: UnifiedUploadedFile[]
  onChange?: (files: UnifiedUploadedFile[]) => void
  uploadModule?: string // 上传模块类型
  resourceType?: ResourceSource // 资源类型，用于文件夹上传
}

const UnifiedUploader: React.FC<UnifiedUploaderProps> = ({
  fileTypes = ['video/*', 'image/*', 'audio/mpeg'],
  maxFiles = 5,
  maxSize = 100 * 1024 * 1024, // 100MB
  multiple = true,
  supportFolder = false,
  onUpload,
  onError,
  className,
  disabled = false,
  value = [],
  onChange,
  folderUuid,
  uploadModule = 'media',
  resourceType = ResourceSource.MEDIA
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UnifiedUploadedFile[]>(value)
  const [lastUploadedIds, setLastUploadedIds] = useState<string[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const folderInputRef = useRef<HTMLInputElement>(null)

  // 使用任务中心 hook
  const { createTask, tasks } = useUploadTasks()
  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()

  // 计算 loading 状态
  const isLoading = useMemo(() => {
    return uploadedFiles.some(file => file.status === 'uploading' || file.status === 'pending') || isUploading
  }, [uploadedFiles, isUploading])

  const updateFiles = useCallback((files: UnifiedUploadedFile[]) => {
    setUploadedFiles(files)
    onChange?.(files)
  }, [onChange])

  // 监听任务状态变化，同步更新本地文件状态
  useEffect(() => {
    const updatedFiles = uploadedFiles.map(file => {
      if (file.taskId) {
        const task = tasks.find(t => t.id === file.taskId)
        if (task) {
          return {
            ...file,
            status: getFileStatusFromTask(task.status),
            progress: task.progress,
            url: task.url,
            error: task.reason || undefined
          }
        }
      }
      return file
    })
    
    // 检查是否有状态变化
    const hasChanges = updatedFiles.some((file, index) => {
      const original = uploadedFiles[index]
      return file.status !== original.status || 
             file.progress !== original.progress ||
             file.url !== original.url ||
             file.error !== original.error
    })
    
    if (hasChanges) {
      setUploadedFiles(updatedFiles)
      onChange?.(updatedFiles)
      
      // 触发成功回调
      const newSuccessfulFiles = updatedFiles.filter(f => 
        f.status === 'success' && !lastUploadedIds.includes(f.id)
      )
      if (newSuccessfulFiles.length > 0) {
        onUpload?.(newSuccessfulFiles)
        setLastUploadedIds(prev => [...prev, ...newSuccessfulFiles.map(f => f.id)])
      }
    }
  }, [tasks, uploadedFiles, onChange, onUpload, lastUploadedIds])

  // 将任务状态转换为文件状态
  const getFileStatusFromTask = useCallback((taskStatus: UploadTask.Status): UnifiedUploadedFile['status'] => {
    switch (taskStatus) {
      case UploadTask.Status.PENDING:
        return 'pending'
      case UploadTask.Status.UPLOADING:
        return 'uploading'
      case UploadTask.Status.COMPLETED:
        return 'success'
      case UploadTask.Status.FAILED:
      case UploadTask.Status.CANCELLED:
        return 'error'
      case UploadTask.Status.PAUSED:
        return 'paused'
      default:
        return 'pending'
    }
  }, [])

  // 获取文件类型
  const getFileType = useCallback((file: File): UploadTask.Type => {
    if (file.type.startsWith('video/')) return UploadTask.Type.VIDEO
    if (file.type.startsWith('audio/')) return UploadTask.Type.AUDIO
    if (file.type.startsWith('image/')) return UploadTask.Type.IMAGE
    return UploadTask.Type.OTHER
  }, [])

  // 创建上传任务
  const createUploadTask = useCallback(async (uploadedFile: UnifiedUploadedFile, targetFolderId?: string) => {
    if (!currentUser) {
      onError?.('用户未登录')
      return
    }

    try {
      const task = await createTask({
        name: uploadedFile.file.name,
        local_path: '', // 将通过 uploadFileContent 传递文件内容
        type: getFileType(uploadedFile.file),
        folder_id: targetFolderId || folderUuid,
        upload_module: uploadModule,
        uid: String(currentUser),
        team_id: currentTeam || 0
      })

      // 更新文件状态，关联任务ID
      setUploadedFiles(prev => {
        const updated = prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, taskId: task.id, status: 'pending' as const }
            : f
        )
        onChange?.(updated)
        return updated
      })

      // 上传文件内容
      const arrayBuffer = await uploadedFile.file.arrayBuffer()
      await window.uploadTask.uploadFileContent({
        taskId: task.id,
        fileContent: arrayBuffer,
        fileName: uploadedFile.file.name
      })

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建上传任务失败'
      setUploadedFiles(prev => {
        const updated = prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, status: 'error' as const, error: errorMessage }
            : f
        )
        onChange?.(updated)
        return updated
      })
      onError?.(errorMessage)
    }
  }, [createTask, currentUser, currentTeam, folderUuid, uploadModule, onChange, onError, getFileType])

  const validateFile = useCallback((file: File): string | null => {
    if (file.size > maxSize) {
      return `文件大小不能超过 ${(maxSize / 1024 / 1024).toFixed(1)}MB`
    }

    const isValidType = fileTypes.some(type => {
      if (type.endsWith('/*')) {
        const category = type.split('/')[0]
        return file.type.startsWith(category + '/')
      }
      return file.type === type
    })

    if (!isValidType) {
      return `不支持的文件类型，支持: ${fileTypes.join(', ')}`
    }

    return null
  }, [fileTypes, maxSize])

  // 处理文件拖拽上传
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (disabled) return

    const totalFiles = uploadedFiles.length + acceptedFiles.length
    if (totalFiles > maxFiles) {
      onError?.(`最多只能上传 ${maxFiles} 个文件`)
      return
    }

    const newFiles: UnifiedUploadedFile[] = acceptedFiles.map(file => {
      const validation = validateFile(file)
      if (validation) {
        onError?.(validation)
        return null
      }

      const id = Math.random().toString(36).substring(2, 11)
      const preview = file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined

      return {
        id,
        file,
        preview,
        folderUuid,
        status: 'pending' as const
      }
    }).filter(Boolean) as UnifiedUploadedFile[]

    const updatedFiles = [...uploadedFiles, ...newFiles]
    updateFiles(updatedFiles)

    // 为每个文件创建上传任务
    newFiles.forEach(file => {
      createUploadTask(file)
    })
  }, [uploadedFiles, maxFiles, validateFile, onError, disabled, updateFiles, createUploadTask, folderUuid])

  // 从文件夹中提取所有符合条件的文件
  const extractFilesFromFolder = useCallback(
    (files: FileList): File[] => {
      const extractedFiles: File[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // 检查文件类型
        const isValidType = fileTypes.some(type => {
          if (type.endsWith('/*')) {
            const category = type.split('/')[0]
            return file.type.startsWith(category + '/')
          }
          return file.type === type
        })

        // 检查文件大小
        if (isValidType && file.size <= maxSize) {
          extractedFiles.push(file)
        }
      }

      return extractedFiles
    },
    [fileTypes, maxSize],
  )

  // 处理文件夹选择
  const handleFolderSelect = useCallback(() => {
    if (disabled || isLoading) return
    folderInputRef.current?.click()
  }, [disabled, isLoading])

  // 处理文件夹输入变化
  const handleFolderInputChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files
      if (!files || files.length === 0) return

      const firstFile = files[0] as any
      const folderPath = firstFile.webkitRelativePath || firstFile.name
      const folderName = folderPath.split('/')[0]

      const extractedFiles = extractFilesFromFolder(files)
      if (extractedFiles.length === 0) {
        onError?.('文件夹中没有找到支持的文件类型')
        return
      }

      if (extractedFiles.length > maxFiles) {
        onError?.(`文件夹中有 ${extractedFiles.length} 个文件，超过最大限制 ${maxFiles} 个`)
        return
      }

      // 创建上传文件对象
      const newFiles: UnifiedUploadedFile[] = extractedFiles.map(file => {
        const id = Math.random().toString(36).substring(2, 11)
        const relativePath = 'webkitRelativePath' in file ? (file as any).webkitRelativePath : (file as File).name
        return {
          id,
          file,
          relativePath,
          folderUuid,
          status: 'pending' as const,
          progress: 0,
        }
      })

      setUploadedFiles(newFiles)
      setIsUploading(true)

      try {
        // 创建文件夹
        let newFolderId: string
        if (resourceType === ResourceSource.LOCAL_STICK) {
          newFolderId = await ResourceModule.paster.dirCreate({ folderName, parentId: folderUuid })
        } else if (resourceType === ResourceSource.LOCAL_MUSIC) {
          newFolderId = await ResourceModule.music.dirCreate({ folderName, parentId: folderUuid })
        } else if (resourceType === ResourceSource.LOCAL_SOUND) {
          newFolderId = await ResourceModule.voice.dirCreate({ folderName, parentId: folderUuid })
        } else {
          newFolderId = await ResourceModule.directory.create({ folderName, parentId: folderUuid })
        }

        // 依次为所有文件创建上传任务
        for (const file of newFiles) {
          await createUploadTask(file, newFolderId)
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '创建文件夹失败'
        onError?.(errorMessage)
      } finally {
        setIsUploading(false)
        // 清空input值，允许重复选择同一个文件夹
        event.target.value = ''
      }
    },
    [extractFilesFromFolder, maxFiles, onError, createUploadTask, folderUuid, resourceType],
  )

  const removeFile = useCallback((fileId: string) => {
    const updatedFiles = uploadedFiles.filter(f => f.id !== fileId)
    updateFiles(updatedFiles)
  }, [uploadedFiles, updateFiles])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: fileTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    multiple,
    disabled
  })

  const getFileIcon = useCallback((file: File) => {
    if (file.type.startsWith('video/')) {
      return <FileVideo className="w-8 h-8 text-blue-500" />
    }
    if (file.type.startsWith('image/')) {
      return <Image className="w-8 h-8 text-green-500" />
    }
    return <Upload className="w-8 h-8 text-gray-500" />
  }, [])

  return (
    <div className={cn('space-y-4', className)}>
      {/* 隐藏的文件夹输入 */}
      {supportFolder && (
        <input
          ref={folderInputRef}
          type="file"
          {...({ webkitdirectory: '' } as any)}
          {...({ mozdirectory: '' } as any)}
          {...({ directory: '' } as any)}
          multiple
          style={{ display: 'none' }}
          onChange={handleFolderInputChange}
          disabled={disabled || isLoading}
          accept=""
        />
      )}

      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
          isDragActive
            ? 'border-primary bg-primary/5'
            : 'border-gray-300 dark:border-gray-600 hover:border-primary',
          disabled && 'opacity-50 cursor-not-allowed',
          isLoading && 'pointer-events-none opacity-75'
        )}
      >
        <input {...getInputProps()} />
        {isLoading ? (
          <Loader2 className="w-12 h-12 mx-auto mb-4 text-primary animate-spin" />
        ) : (
          <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        )}
        <p className="text-lg font-medium mb-2">
          {isLoading
            ? '正在上传...'
            : isDragActive
              ? '释放文件到这里'
              : '拖拽文件到这里或点击上传'}
        </p>
        <p className="text-sm text-gray-500">
          支持 {fileTypes.join(', ')} 格式，最大 {(maxSize / 1024 / 1024).toFixed(1)}MB
        </p>
      </div>

      {/* 文件夹上传按钮 */}
      {supportFolder && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            disabled={disabled || isLoading}
            onClick={handleFolderSelect}
            className="flex items-center gap-2"
          >
            <FolderOpen className="w-4 h-4" />
            选择文件夹上传
          </Button>
        </div>
      )}

      {/* 文件列表 */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          {uploadedFiles.map(uploadedFile => (
            <div
              key={uploadedFile.id}
              className="flex items-center space-x-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800"
            >
              {/* File Icon/Preview */}
              <div className="flex-shrink-0">
                {uploadedFile.preview ? (
                  <img
                    src={uploadedFile.preview}
                    alt="Preview"
                    className="w-12 h-12 object-cover rounded"
                  />
                ) : (
                  getFileIcon(uploadedFile.file)
                )}
              </div>

              {/* File Info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">
                  {uploadedFile.file.name}
                </p>
                {uploadedFile.relativePath && (
                  <p className="text-xs text-blue-600 dark:text-blue-400 truncate flex items-center">
                    <Folder className="w-3 h-3 mr-1 flex-shrink-0" />
                    {uploadedFile.relativePath}
                  </p>
                )}
                <p className="text-xs text-gray-500">
                  {(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB
                </p>

                {/* Status Display */}
                {uploadedFile.status === 'pending' && (
                  <div className="flex items-center mt-2 text-gray-500">
                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                    <span className="text-xs">等待上传...</span>
                  </div>
                )}

                {uploadedFile.status === 'uploading' && (
                  <div className="mt-2">
                    <div className="flex items-center text-blue-500 mb-1">
                      <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      <span className="text-xs">
                        正在上传... {uploadedFile.progress ? `${Math.round(uploadedFile.progress * 100)}%` : ''}
                      </span>
                    </div>
                    {/* 进度条 */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(uploadedFile.progress || 0) * 100}%` }}
                      />
                    </div>
                  </div>
                )}

                {uploadedFile.status === 'paused' && (
                  <div className="flex items-center mt-2 text-yellow-500">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    <span className="text-xs">已暂停</span>
                  </div>
                )}

                {uploadedFile.status === 'success' && (
                  <div className="flex items-center mt-2 text-green-500">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    <span className="text-xs">上传成功</span>
                    {uploadedFile.url && (
                      <div className="text-xs text-blue-500 hover:text-blue-700 ml-2 underline">
                        {uploadedFile.url}
                      </div>
                    )}
                  </div>
                )}

                {uploadedFile.status === 'error' && (
                  <div className="flex items-center mt-2 text-red-500">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    <span className="text-xs">{uploadedFile.error}</span>
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(uploadedFile.id)}
                className="text-gray-500 hover:text-red-500"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export { UnifiedUploader }
